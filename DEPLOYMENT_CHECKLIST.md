# 🚀 Koyeb Deployment Checklist

## ✅ Pre-Deployment Status
- [x] **Repository Ready**: Code committed and pushed to GitHub
- [x] **Supabase Database**: PostgreSQL configured and tested
- [x] **Environment Variables**: All credentials prepared
- [x] **Build Configuration**: Scripts updated for production
- [x] **Documentation**: Deployment guides created

## 🔧 Deployment Steps

### Step 1: Access Koyeb Dashboard
1. Go to [app.koyeb.com](https://app.koyeb.com)
2. Sign in to your Koyeb account
3. Click "Create Web Service"

### Step 2: Configure GitHub Integration
1. **Deployment Method**: Select "GitHub"
2. **Repository**: Choose `bademarc/edgen` (or your repository name)
3. **Branch**: Select `master`
4. **Service Name**: `layeredge-edgen-community`

### Step 3: Build Configuration
```
Build Command: npm run build
Start Command: npm start
Port: 3000
```

### Step 4: Environment Variables
Copy and paste these exact values in Koyeb:

#### Database (Supabase)
```
DATABASE_URL=postgres://postgres.bzqayhnlogpaxfcmmrlq:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1

DIRECT_URL=postgres://postgres.bzqayhnlogpaxfcmmrlq:<EMAIL>:5432/postgres
```

#### NextAuth.js
```
NEXTAUTH_URL=https://layeredge-edgen-community-[YOUR-ORG].koyeb.app
NEXTAUTH_SECRET=layeredge-edgen-community-secret-key-2024
```

#### Twitter/X API
```
TWITTER_CLIENT_ID=SzVkU3VsQ0NheWcwMVU1MW8ta1I6MTpjaQ
TWITTER_CLIENT_SECRET=snl_S5q_2RZ1Bk6V7GCyDUoNWuAHxFjnf6Za7W-F2qMz3UUvLS
TWITTER_BEARER_TOKEN=AAAAAAAAAAAAAAAAAAAAAHyCzwEAAAAAh9uE7X3FHoLzdxGTfVwuDVkhDV4%3DcbbsrKkuHDiBFC0PGANM7jD8vrLOd0tnlhr30brsLmXUAxHFTZ
```

#### Application
```
LAYEREDGE_COMMUNITY_URL=https://x.com/i/communities/1890107751621357663
NODE_ENV=production
```

### Step 5: Deploy
1. Review all settings
2. Click "Deploy"
3. Monitor build logs

## 🔍 Build Process Monitoring

Watch for these key steps in the build logs:
1. ✅ Dependencies installation
2. ✅ Prisma client generation
3. ✅ Database migration deployment
4. ✅ Next.js build completion

## 📋 Post-Deployment Tasks

### 1. Verify Application
- [ ] Application loads at deployment URL
- [ ] Homepage displays correctly
- [ ] Navigation works between pages
- [ ] Leaderboard shows user data (confirms database connection)

### 2. Update Twitter OAuth
- [ ] Go to [Twitter Developer Portal](https://developer.twitter.com)
- [ ] Update callback URL to: `https://your-app-url.koyeb.app/api/auth/callback/twitter`

### 3. Test Authentication
- [ ] Click "Sign in with X"
- [ ] Verify OAuth redirect works
- [ ] Confirm user can authenticate

### 4. Test Core Features
- [ ] Submit a tweet URL
- [ ] Check points calculation
- [ ] Verify leaderboard updates

## 🚨 Troubleshooting

### If Build Fails:
1. Check environment variables are set correctly
2. Verify DATABASE_URL and DIRECT_URL format
3. Ensure Supabase database is accessible

### If App Doesn't Load:
1. Check runtime logs in Koyeb dashboard
2. Verify port 3000 is configured
3. Check NEXTAUTH_URL matches deployment URL

### If Database Connection Fails:
1. Verify Supabase connection strings
2. Check database status in Supabase dashboard
3. Ensure IPv4 pooler endpoints are used

## 🎯 Success Criteria

Deployment is successful when:
- ✅ Build completes without errors
- ✅ Application accessible via Koyeb URL
- ✅ Database queries work (leaderboard populated)
- ✅ Authentication flow functional
- ✅ All pages load correctly

## 📞 Next Steps After Deployment

1. **Monitor Performance**: Use Koyeb and Supabase dashboards
2. **Set Up Monitoring**: Configure alerts for downtime
3. **Update DNS** (if using custom domain)
4. **Test Load**: Verify application handles traffic

---

**Ready to deploy?**
1. Open [app.koyeb.com](https://app.koyeb.com)
2. Follow the steps above
3. Monitor the build process
4. Verify deployment success

**Repository**: https://github.com/bademarc/edgen
**Branch**: master
**Status**: ✅ Ready for deployment
