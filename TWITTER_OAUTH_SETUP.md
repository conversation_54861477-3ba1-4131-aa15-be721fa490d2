# Twitter OAuth Setup for Koyeb Deployment

## 🔧 Twitter Developer Portal Configuration

### Step 1: Update OAuth Settings

1. **Go to Twitter Developer Portal**
   - Visit [developer.twitter.com](https://developer.twitter.com)
   - Navigate to your app dashboard
   - Select your LayerEdge community app

2. **Update OAuth 2.0 Settings**
   - Go to "App settings" → "Authentication settings"
   - Click "Edit" on OAuth 2.0 settings

3. **Configure Callback URLs**
   ```
   # For Koyeb deployment
   https://edgen.koyeb.app/api/auth/callback/twitter
   
   # For local development (keep both)
   http://localhost:3000/api/auth/callback/twitter
   ```

4. **Configure Website URL**
   ```
   https://edgen.koyeb.app
   ```

5. **Set App Permissions**
   - Read permissions (minimum required)
   - Request email address: Optional but recommended

### Step 2: Verify Environment Variables

Ensure these are set in your Koyeb deployment:

```bash
# Twitter OAuth Credentials
TWITTER_CLIENT_ID=SzVkU3VsQ0NheWcwMVU1MW8ta1I6MTpjaQ
TWITTER_CLIENT_SECRET=snl_S5q_2RZ1Bk6V7GCyDUoNWuAHxFjnf6Za7W-F2qMz3UUvLS

# NextAuth Configuration
NEXTAUTH_URL=https://edgen.koyeb.app
NEXTAUTH_SECRET=layeredge-edgen-community-secret-key-2024

# Database URLs
DATABASE_URL=postgres://postgres.bzqayhnlogpaxfcmmrlq:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1
DIRECT_URL=postgres://postgres.bzqayhnlogpaxfcmmrlq:<EMAIL>:5432/postgres
```

## 🐛 Troubleshooting Authentication Issues

### Issue 1: "Неправильный пароль" (Incorrect Password)

**Cause**: This Russian error message typically indicates a Twitter OAuth configuration issue.

**Solutions**:
1. Verify callback URL is exactly: `https://edgen.koyeb.app/api/auth/callback/twitter`
2. Check that `NEXTAUTH_URL` matches your Koyeb deployment URL
3. Ensure Twitter app has correct permissions

### Issue 2: Database "Record to update not found" Error

**Cause**: The old signIn callback was trying to update a user before it was created.

**Solution**: ✅ **FIXED** - Updated auth configuration to use `events.signIn` instead of `callbacks.signIn`

### Issue 3: Redirect to x.com/home

**Cause**: OAuth flow is not completing properly due to configuration mismatch.

**Solutions**:
1. Verify all callback URLs are configured in Twitter Developer Portal
2. Check that `NEXTAUTH_SECRET` is set and consistent
3. Ensure database connection is working

## 🔍 Testing the Fix

### Local Testing
```bash
# Test locally first
npm run dev

# Try signing in with Twitter
# Check browser console for any errors
```

### Production Testing
1. Deploy the updated auth configuration to Koyeb
2. Test the Twitter OAuth flow
3. Check Koyeb logs for any errors:
   ```bash
   # Look for these log messages:
   "Updating user with Twitter data"
   "Successfully updated user with Twitter data"
   ```

## 📋 Deployment Checklist

- [ ] Updated `src/lib/auth.ts` with fixed authentication flow
- [ ] Configured Twitter callback URL: `https://edgen.koyeb.app/api/auth/callback/twitter`
- [ ] Set `NEXTAUTH_URL=https://edgen.koyeb.app` in Koyeb environment
- [ ] Verified all Twitter OAuth credentials are correct
- [ ] Tested database connection is working
- [ ] Deployed to Koyeb and tested authentication flow

## 🚀 Expected Behavior After Fix

1. **User clicks "Sign in with X"** → Redirects to Twitter OAuth
2. **User authorizes app** → Redirects back to your app
3. **NextAuth processes callback** → Creates/updates user in database
4. **User is signed in** → Redirected to dashboard

## 📞 Support

If you continue to experience issues:
1. Check Koyeb deployment logs
2. Verify Twitter Developer Portal settings
3. Test database connectivity
4. Ensure all environment variables are set correctly
