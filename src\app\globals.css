@import "tailwindcss";

:root {
  /* LayerEdge Brand Colors */
  --background: #0a0a0a;
  --foreground: #ffffff;
  --primary: #f59e0b; /* LayerEdge orange/amber */
  --primary-foreground: #000000;
  --secondary: #1f2937;
  --secondary-foreground: #e5e7eb;
  --accent: #3b82f6; /* LayerEdge blue accent */
  --accent-foreground: #ffffff;
  --muted: #374151;
  --muted-foreground: #9ca3af;
  --border: #2d3748;
  --input: #1a202c;
  --ring: #f59e0b;
  --card: #111827;
  --card-foreground: #f9fafb;

  /* LayerEdge specific colors */
  --layeredge-orange: #f59e0b;
  --layeredge-orange-light: #fbbf24;
  --layeredge-orange-dark: #d97706;
  --layeredge-blue: #3b82f6;
  --layeredge-grid: #1a202c;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* LayerEdge brand colors */
  --color-layeredge-orange: var(--layeredge-orange);
  --color-layeredge-orange-light: var(--layeredge-orange-light);
  --color-layeredge-orange-dark: var(--layeredge-orange-dark);
  --color-layeredge-blue: var(--layeredge-blue);
  --color-layeredge-grid: var(--layeredge-grid);
}

* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--muted-foreground);
}

/* Smooth animations */
* {
  transition: all 0.2s ease-in-out;
}

/* Focus styles */
*:focus {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

/* LayerEdge Grid Pattern */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(245, 158, 11, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(245, 158, 11, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-grid-pattern-subtle {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
}

/* LayerEdge Gradient Text */
.text-layeredge-gradient {
  background: linear-gradient(135deg, var(--layeredge-orange) 0%, var(--layeredge-orange-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* LayerEdge Button Styles */
.btn-layeredge-primary {
  background: linear-gradient(135deg, var(--layeredge-orange) 0%, var(--layeredge-orange-light) 100%);
  color: #000000;
  font-weight: 600;
  transition: all 0.2s ease-in-out;
}

.btn-layeredge-primary:hover {
  background: linear-gradient(135deg, var(--layeredge-orange-dark) 0%, var(--layeredge-orange) 100%);
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(245, 158, 11, 0.2);
}

/* LayerEdge Card Styles */
.card-layeredge {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 12px;
  transition: all 0.3s ease-in-out;
}

.card-layeredge:hover {
  border-color: var(--layeredge-orange);
  box-shadow: 0 8px 32px rgba(245, 158, 11, 0.1);
  transform: translateY(-2px);
}
